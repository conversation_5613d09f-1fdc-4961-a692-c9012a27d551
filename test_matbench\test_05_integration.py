#!/usr/bin/env python3
"""
测试模块5: 完整集成
对应功能: DataLoader, 批处理, 训练配置
"""
import sys
from pathlib import Path

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_dataloader_integration():
    """测试DataLoader集成"""
    from ppmat.datasets.matbench_dataset import MatBenchDataset
    from paddle.io import DataLoader
    from ppmat.datasets.collate_fn import DefaultCollator
    
    # 创建数据集
    dataset = MatBenchDataset(
        path="./data/matbench/matbench_mp_e_form_full.pkl",
        property_names=["e_form"],
        build_structure_cfg={
            "format": "pymatgen_structure",
            "num_cpus": 1
        },
        build_graph_cfg=None,  # 不构建图，简化测试
        max_samples=8,
        overwrite=True
    )
    
    print(f"DataLoader集成测试:")
    print(f"  数据集样本数: {len(dataset)}")
    
    # 创建数据加载器
    dataloader = DataLoader(
        dataset,
        batch_size=4,
        shuffle=False,
        num_workers=0,
        collate_fn=DefaultCollator(),
        drop_last=False
    )
    
    print(f"  数据加载器创建成功")
    
    # 测试批处理
    batch_count = 0
    for batch in dataloader:
        batch_count += 1
        print(f"  批次 {batch_count}:")
        print(f"    键: {list(batch.keys())}")
        
        for key, value in batch.items():
            if hasattr(value, 'shape'):
                print(f"    {key}: 形状={value.shape}")
            elif isinstance(value, list):
                print(f"    {key}: 列表长度={len(value)}")
        
        # 验证批次格式
        assert 'graph' in batch
        assert 'e_form' in batch
        assert 'id' in batch
        
        if batch_count >= 2:  # 只测试前2个批次
            break
    
    print(f"✅ DataLoader集成测试通过，处理了 {batch_count} 个批次")
    return True

def test_config_compatibility():
    """测试配置文件兼容性"""
    from omegaconf import OmegaConf
    
    config_path = Path(__file__).parent / "test_config_1000.yaml"
    
    print(f"配置文件兼容性测试:")
    print(f"  配置文件: {config_path}")
    
    # 加载配置
    config = OmegaConf.load(config_path)
    
    print(f"  配置加载成功")
    print(f"  数据集类: {config.Dataset.dataset.__class_name__}")
    print(f"  最大样本数: {config.Dataset.dataset.__init_params__.max_samples}")
    
    # 验证配置结构
    assert hasattr(config, 'Dataset')
    assert hasattr(config.Dataset, 'dataset')
    assert config.Dataset.dataset.__class_name__ == "MatBenchDataset"
    
    print(f"✅ 配置文件兼容性测试通过")
    return True

if __name__ == "__main__":
    print("🎯 测试模块5: 完整集成")
    print("=" * 40)
    
    data_file = project_root / "data" / "matbench" / "matbench_mp_e_form_full.pkl"
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        exit(1)
    
    try:
        test_dataloader_integration()
        test_config_compatibility()
        print("\n🎉 模块5测试成功")
    except Exception as e:
        print(f"\n❌ 模块5测试失败: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
