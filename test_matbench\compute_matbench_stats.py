# scripts/compute_matbench_stats.py
from matminer.datasets import load_dataset
import numpy as np

def compute_dataset_stats(task_name="matbench_mp_e_form"):
    """计算数据集统计信息用于模型配置"""
    
    df = load_dataset(task_name)
    target_col = "e_form"  # 根据任务调整
    
    values = df[target_col].values
    mean = np.mean(values)
    std = np.std(values)
    
    print(f"Dataset: {task_name}")
    print(f"Samples: {len(df)}")
    print(f"Target: {target_col}")
    print(f"Mean: {mean:.4f}")
    print(f"Std: {std:.4f}")
    print(f"Min: {np.min(values):.4f}")
    print(f"Max: {np.max(values):.4f}")
    
    return {"mean": mean, "std": std}

if __name__ == "__main__":
    stats = compute_dataset_stats()