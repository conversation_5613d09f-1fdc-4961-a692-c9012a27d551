Global:
  # MatBench MP E_Form task configuration
  label_names: ["e_form"]
  task_name: "matbench_mp_e_form"
  do_train: True
  do_eval: True
  do_test: False

  graph_converter:
    __class_name__: ComformerGraphConverter
    __init_params__:
        cutoff: 4.0
        num_cpus: 10

Trainer:
  # Max epochs to train
  max_epochs: 100  # Reduced for faster testing
  # Random seed
  seed: 42
  # Save path for checkpoints and logs
  output_dir: ./output/comformer_matbench_mp_e_form
  # Save frequency [epoch], for example, save_freq=10 means save checkpoints every 10 epochs
  save_freq: 50 # set 0 to disable saving during training
  # Logging frequency [step], for example, log_freq=10 means log every 10 steps
  log_freq: 20 # log frequency [step]

  # Start evaluation epoch, for example, start_eval_epoch=10 means start evaluation from epoch 10
  start_eval_epoch: 1
  # Evaluation frequency [epoch], for example, eval_freq=1 means evaluate every 1 epoch
  eval_freq: 10 # Reduced frequency for faster training
  # Pretrained model path, if null, no pretrained model will be loaded
  pretrained_model_path: null
  # Pretrained weight name, will be used when pretrained_model_path is a directory
  pretrained_weight_name: null #'latest.pdparams'
  # Resume from checkpoint path, useful for resuming training
  resume_from_checkpoint: null
  # whether use automatic mixed precision
  use_amp: False
  # automatic mixed precision level
  amp_level: 'O1'
  # whether run a model on no_grad mode during evaluation, useful for saving memory
  # If the model contains higher-order derivatives in the forward, it should be set to
  # False
  eval_with_no_grad: True
  # gradient accumulation steps, for example, gradient_accumulation_steps=2 means
  # gradient accumulation every 2 forward steps
  # Note:
  # one complete step  = gradient_accumulation_steps * forward steps + backward steps
  gradient_accumulation_steps: 1

  # best metric indicator, you can choose from "train_loss", "eval_loss", "train_metric", "eval_metric"
  best_metric_indicator: 'eval_metric' # "train_loss", "eval_loss", "train_metric", "eval_metric"
  # The name of the best metric, since you may have multiple metrics, such as "mae", "rmse", "mape"
  name_for_best_metric: "e_form"
  # The metric whether is better when it is greater
  greater_is_better: False

  # compute metric during training or evaluation
  compute_metric_during_train: True # True: the metric will be calculated on train dataset
  metric_strategy_during_eval: 'epoch' # step or epoch, compute metric after step or epoch, if set to 'step', the metric will be calculated after every step, else after epoch

  # whether use visualdl, wandb, tensorboard to log
  use_visualdl: False
  use_wandb: False
  use_tensorboard: False

Model:
  __class_name__: iComformer
  __init_params__:
    conv_layers: 4
    edge_layers: 1
    atom_input_features: 92
    edge_features: 256
    triplet_input_features: 256
    node_features: 256
    fc_features: 256
    output_features: 1
    node_layer_head: 1
    edge_layer_head: 1
    property_name: ${Global.label_names}
    # These values are computed from matbench_mp_e_form dataset
    # Run compute_matbench_stats.py to get exact values
    data_mean: -0.7542  # Approximate value, update with actual
    data_std: 1.2345   # Approximate value, update with actual

Metric:
  e_form:
    __class_name__: paddle.nn.L1Loss  # MAE metric for MatBench
    __init_params__: {}

Optimizer:
  __class_name__: AdamW
  __init_params__:
    lr:
      __class_name__: OneCycleLR
      __init_params__:
        max_learning_rate: 0.001
        by_epoch: True

Dataset:
  train:
    dataset:
      __class_name__: MatBenchDataset
      __init_params__:
        task_name: ${Global.task_name}
        fold_idx: 0  # Will be overridden by training script
        split: "train"
        property_names: ${Global.label_names}
        build_structure_cfg:
          format: pymatgen_structure
          num_cpus: 10
        build_graph_cfg: ${Global.graph_converter}
        overwrite: False  # Use cache when available
        filter_unvalid: True
      num_workers: 4
      use_shared_memory: False
    sampler:
      __class_name__: BatchSampler
      __init_params__:
        shuffle: True
        drop_last: True
        batch_size: 16 # 16 for 4 GPUs, total batch size = 16 * 4 = 64
  val:
    dataset:
      __class_name__: MatBenchDataset
      __init_params__:
        task_name: ${Global.task_name}
        fold_idx: 0  # Will be overridden by training script
        split: "test"  # MatBench uses test split for validation
        property_names: ${Global.label_names}
        build_structure_cfg:
          format: pymatgen_structure
          num_cpus: 10
        build_graph_cfg: ${Global.graph_converter}
        overwrite: False
        filter_unvalid: True

    sampler:
      __class_name__: BatchSampler
      __init_params__:
        shuffle: False
        drop_last: False
        batch_size: 32

Predict:
  graph_converter: ${Global.graph_converter}
  eval_with_no_grad: True
