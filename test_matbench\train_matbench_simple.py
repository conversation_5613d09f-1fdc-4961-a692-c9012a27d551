#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Simplified MatBench training script based on existing train.py.
Supports single fold training and basic cross-validation.
"""

import argparse
import os
import sys
from pathlib import Path
import json
import time

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import paddle
from omegaconf import OmegaConf

from ppmat.datasets import build_dataloader
from ppmat.metrics import build_metric
from ppmat.models import build_model
from ppmat.optimizer import build_optimizer
from ppmat.trainer.base_trainer import BaseTrainer
from ppmat.utils import logger


def modify_config_for_fold(config, fold_idx, task_name="matbench_mp_e_form"):
    """
    Modify configuration for specific fold.
    
    Args:
        config: Original configuration
        fold_idx: Fold index (0-4)
        task_name: MatBench task name
    
    Returns:
        Modified configuration
    """
    # Deep copy config
    config = OmegaConf.create(config)
    
    # Update dataset configurations
    if "Dataset" in config:
        for split in ["train", "val", "test"]:
            if split in config["Dataset"]:
                dataset_cfg = config["Dataset"][split]["dataset"]
                if "__init_params__" in dataset_cfg:
                    dataset_cfg["__init_params__"]["fold_idx"] = fold_idx
                    dataset_cfg["__init_params__"]["task_name"] = task_name
                    
                    # Set split appropriately
                    if split == "train":
                        dataset_cfg["__init_params__"]["split"] = "train"
                    else:  # val and test both use test split in MatBench
                        dataset_cfg["__init_params__"]["split"] = "test"
    
    # Update output directory
    if "Trainer" in config and "output_dir" in config["Trainer"]:
        base_output = config["Trainer"]["output_dir"]
        config["Trainer"]["output_dir"] = f"{base_output}_fold_{fold_idx}"
    
    return config


def train_single_fold(config_path, fold_idx, task_name="matbench_mp_e_form", max_epochs=None):
    """
    Train a single fold.
    
    Args:
        config_path: Path to configuration file
        fold_idx: Fold index (0-4)
        task_name: MatBench task name
        max_epochs: Override max epochs (for quick testing)
    
    Returns:
        Training results dictionary
    """
    
    print(f"\n{'='*60}")
    print(f"Training Fold {fold_idx} - {task_name}")
    print(f"{'='*60}")
    
    # Load and modify config
    config = OmegaConf.load(config_path)
    config = modify_config_for_fold(config, fold_idx, task_name)
    
    # Override max epochs if specified
    if max_epochs is not None:
        config["Trainer"]["max_epochs"] = max_epochs
        print(f"Override max_epochs to {max_epochs} for quick testing")
    
    # Create output directory
    output_dir = config["Trainer"]["output_dir"]
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # Build dataloaders
        print("Building dataloaders...")
        train_loader = None
        val_loader = None
        
        if config["Global"].get("do_train", True):
            train_data_cfg = config["Dataset"].get("train")
            if train_data_cfg is not None:
                train_loader = build_dataloader(train_data_cfg)
                print(f"✓ Train loader: {len(train_loader)} batches")
        
        if config["Global"].get("do_eval", False) or config["Global"].get("do_train", True):
            val_data_cfg = config["Dataset"].get("val")
            if val_data_cfg is not None:
                val_loader = build_dataloader(val_data_cfg)
                print(f"✓ Val loader: {len(val_loader)} batches")
        
        # Build model
        print("Building model...")
        model_cfg = config.get("Model")
        model = build_model(model_cfg)
        print(f"✓ Model built: {model.__class__.__name__}")
        
        # Build optimizer
        optimizer = None
        lr_scheduler = None
        if config.get("Optimizer") is not None and config["Global"].get("do_train", True):
            print("Building optimizer...")
            optimizer, lr_scheduler = build_optimizer(
                config["Optimizer"],
                model,
                config["Trainer"]["max_epochs"],
                len(train_loader) if train_loader else 1,
            )
            print(f"✓ Optimizer built: {optimizer.__class__.__name__}")
        
        # Build metrics
        metric_func = None
        metric_cfg = config.get("Metric")
        if metric_cfg is not None:
            print("Building metrics...")
            metric_func = build_metric(metric_cfg)
            print(f"✓ Metrics built")
        
        # Initialize trainer
        print("Initializing trainer...")
        trainer = BaseTrainer(
            config["Trainer"],
            model,
            train_dataloader=train_loader,
            val_dataloader=val_loader,
            optimizer=optimizer,
            lr_scheduler=lr_scheduler,
            compute_metric_func_dict=metric_func,
        )
        print(f"✓ Trainer initialized")
        
        # Training
        start_time = time.time()
        results = {}
        
        if config["Global"].get("do_train", True):
            print("Starting training...")
            trainer.train()
            training_time = time.time() - start_time
            print(f"✓ Training completed in {training_time:.2f}s")
            results["training_time"] = training_time
        
        # Evaluation
        if config["Global"].get("do_eval", False) and val_loader is not None:
            print("Evaluating...")
            time_info, loss_info, metric_info = trainer.eval(val_loader)
            results["eval_time"] = time_info
            results["eval_loss"] = loss_info
            results["eval_metrics"] = metric_info
            
            # Extract best metric for MatBench reporting
            if metric_info and len(metric_info) > 0:
                # Assume first metric is the main one (e.g., MAE)
                main_metric_name = list(metric_info.keys())[0]
                results["best_metric"] = metric_info[main_metric_name]
                print(f"✓ Evaluation completed - {main_metric_name}: {results['best_metric']:.6f}")
        
        # Save results
        results_file = os.path.join(output_dir, "fold_results.json")
        results["fold_idx"] = fold_idx
        results["task_name"] = task_name
        results["config_path"] = str(config_path)
        
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"✓ Results saved to {results_file}")
        return results
        
    except Exception as e:
        print(f"✗ Training failed: {e}")
        import traceback
        traceback.print_exc()
        return None


def run_cross_validation(config_path, task_name="matbench_mp_e_form", folds=None, max_epochs=None):
    """
    Run cross-validation training.
    
    Args:
        config_path: Path to configuration file
        task_name: MatBench task name
        folds: List of fold indices to run (default: [0, 1, 2, 3, 4])
        max_epochs: Override max epochs for quick testing
    
    Returns:
        Cross-validation results
    """
    
    if folds is None:
        folds = [0, 1, 2, 3, 4]
    
    print(f"Starting {len(folds)}-fold cross-validation for {task_name}")
    print(f"Folds: {folds}")
    
    fold_results = []
    
    for fold_idx in folds:
        result = train_single_fold(config_path, fold_idx, task_name, max_epochs)
        fold_results.append(result)
        
        if result is None:
            print(f"⚠️  Fold {fold_idx} failed, continuing with next fold...")
    
    # Compute summary
    valid_results = [r for r in fold_results if r is not None and r.get("best_metric") is not None]
    
    if valid_results:
        import numpy as np
        metrics = [r["best_metric"] for r in valid_results]
        
        summary = {
            "task_name": task_name,
            "total_folds": len(folds),
            "successful_folds": len(valid_results),
            "mean_metric": float(np.mean(metrics)),
            "std_metric": float(np.std(metrics)),
            "min_metric": float(np.min(metrics)),
            "max_metric": float(np.max(metrics)),
            "fold_metrics": metrics,
            "fold_results": valid_results
        }
        
        print(f"\n{'='*60}")
        print("CROSS-VALIDATION SUMMARY")
        print(f"{'='*60}")
        print(f"Task: {summary['task_name']}")
        print(f"Successful folds: {summary['successful_folds']}/{summary['total_folds']}")
        print(f"Mean MAE: {summary['mean_metric']:.6f} ± {summary['std_metric']:.6f}")
        print(f"Range: [{summary['min_metric']:.6f}, {summary['max_metric']:.6f}]")
        
        # Save summary
        base_config = OmegaConf.load(config_path)
        output_dir = base_config["Trainer"]["output_dir"]
        summary_file = f"{output_dir}_cv_summary.json"
        
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        print(f"Summary saved to: {summary_file}")
        return summary
    else:
        print("❌ No successful folds found")
        return None


def main():
    parser = argparse.ArgumentParser(description="Simplified MatBench training")
    parser.add_argument("-c", "--config", required=True, help="Config file path")
    parser.add_argument("--fold", type=int, help="Single fold to train (0-4)")
    parser.add_argument("--folds", nargs="+", type=int, default=[0, 1, 2, 3, 4], 
                       help="Folds to train for CV")
    parser.add_argument("--task", default="matbench_mp_e_form", help="MatBench task name")
    parser.add_argument("--max-epochs", type=int, help="Override max epochs for quick testing")
    parser.add_argument("--quick-test", action="store_true", help="Quick test with 2 epochs")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.config):
        print(f"Config file not found: {args.config}")
        return 1
    
    # Quick test mode
    if args.quick_test:
        args.max_epochs = 2
        args.folds = [0, 1]  # Only test 2 folds
        print("🚀 Quick test mode: 2 epochs, 2 folds")
    
    try:
        if args.fold is not None:
            # Single fold training
            result = train_single_fold(args.config, args.fold, args.task, args.max_epochs)
            return 0 if result is not None else 1
        else:
            # Cross-validation
            summary = run_cross_validation(args.config, args.task, args.folds, args.max_epochs)
            return 0 if summary is not None else 1
            
    except KeyboardInterrupt:
        print("\n⚠️  Training interrupted by user")
        return 1
    except Exception as e:
        print(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
