#!/usr/bin/env python3
"""
测试模块3: 图构建
对应方法: build_graphs
"""
import sys
import os
from pathlib import Path

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_graph_building():
    """测试图构建功能"""
    from ppmat.datasets.matbench_dataset import MatBenchDataset
    from ppmat.datasets.build_structure import BuildStructure
    from ppmat.models import build_graph_converter
    
    # 创建数据集实例
    dataset = MatBenchDataset.__new__(MatBenchDataset)
    dataset.path = "./data/matbench/matbench_mp_e_form_full.pkl"
    dataset.max_samples = 2
    dataset.cache_path = "./data/matbench_cache/test_graph"
    dataset.overwrite = True
    
    # 读取数据并构建结构
    data, num_samples = dataset.read_data(dataset.path)
    dataset.row_data = data['structure']
    dataset.num_samples = num_samples
    
    # 构建结构
    build_structure_cfg = {
        "format": "pymatgen_structure",
        "primitive": False,
        "niggli": True,
        "num_cpus": 1,
    }
    dataset.build_structure = BuildStructure(**build_structure_cfg)
    dataset.structures = dataset.build_structures(overwrite=True)
    
    # 设置图转换器
    build_graph_cfg = {
        "__class_name__": "ComformerGraphConverter",
        "__init_params__": {
            "cutoff": 5.0,
            "num_cpus": 1,
            "atom_features": "cgcnn",
            "max_neighbors": 25
        }
    }
    dataset.graph_converter = build_graph_converter(build_graph_cfg)
    
    # 测试build_graphs方法
    graphs = dataset.build_graphs(overwrite=True)
    
    print(f"图构建结果:")
    print(f"  图数量: {len(graphs)}")
    print(f"  图路径示例: {graphs[0]}")
    
    # 验证图文件
    assert len(graphs) == num_samples
    assert all(os.path.exists(g) for g in graphs)
    
    # 测试加载图
    import pickle
    with open(graphs[0], 'rb') as f:
        graph = pickle.load(f)
    
    print(f"  图类型: {type(graph)}")
    if hasattr(graph, 'num_nodes'):
        print(f"  节点数: {graph.num_nodes}")
    if hasattr(graph, 'num_edges'):
        print(f"  边数: {graph.num_edges}")
    
    print("✅ 图构建模块测试通过")
    return True

if __name__ == "__main__":
    print("🎯 测试模块3: 图构建")
    print("=" * 40)
    
    data_file = project_root / "data" / "matbench" / "matbench_mp_e_form_full.pkl"
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        exit(1)
    
    try:
        test_graph_building()
        print("\n🎉 模块3测试成功")
    except Exception as e:
        print(f"\n❌ 模块3测试失败: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
