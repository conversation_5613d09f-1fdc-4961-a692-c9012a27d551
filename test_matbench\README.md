# MatBenchDataset Testing

This directory contains test scripts for the MatBenchDataset implementation.

## Prerequisites

Before running tests, make sure you have the required dependencies installed:

```bash
# Install matbench and matminer
pip install matbench matminer

# Or if you encounter installation issues, try:
conda install -c conda-forge matbench matminer
```

## Test Scripts

### 1. Basic Functionality Test (`test_basic.py`)

Tests core functionality of MatBenchDataset:
- Import verification
- Data loading from matminer
- Dataset creation
- Cross-validation splits

```bash
python test_matbench/test_basic.py
```

### 2. Comprehensive Test Suite (`test_matbench_dataset.py`)

Complete test suite covering all modules:
- MatMiner availability
- Dataset initialization
- Cross-validation splits
- Data access and format
- Caching mechanism
- DataLoader integration
- Error handling

```bash
python test_matbench/test_matbench_dataset.py
```

### 3. Data Statistics (`compute_matbench_stats.py`)

Computes dataset statistics needed for model configuration:

```bash
python test_matbench/compute_matbench_stats.py
```

### 4. Run All Tests (`run_tests.py`)

Runs all available tests in sequence:

```bash
python test_matbench/run_tests.py
```

## Expected Output

### Successful Test Run

```
=== Testing Imports ===
✓ matminer imported successfully
✓ MatBenchDataset imported successfully
✓ Other dependencies imported successfully

=== Testing MatMiner Data Loading ===
Loading matbench_mp_e_form dataset...
✓ Dataset loaded successfully
  Samples: 132752
  Columns: ['structure', 'e_form']
✓ Required columns found
  First sample e_form: -1.0234
  Structure type: <class 'pymatgen.core.structure.Structure'>

=== Testing Dataset Creation ===
Creating MatBenchDataset...
✓ Dataset created successfully
  Task: matbench_mp_e_form
  Samples: 106201
  Properties: ['e_form']
✓ Data access works
  Sample keys: ['e_form', 'id', 'structure_array']
  e_form value: [-1.0234]

=== Testing Cross-Validation ===
Testing 5-fold cross-validation...
  Fold 0: Train=106201, Test=26551
  Fold 1: Train=106202, Test=26550
✓ Cross-validation splits look reasonable

==================================================
SUMMARY
==================================================
Import Test         : ✓ PASS
MatMiner Loading    : ✓ PASS
Dataset Creation    : ✓ PASS
Cross-Validation    : ✓ PASS

Result: 4/4 tests passed
🎉 All basic tests passed!
```

## Troubleshooting

### Common Issues

1. **MatMiner Installation Error**
   ```
   ERROR: matminer import failed: No module named 'matminer'
   ```
   **Solution**: Install matminer with `pip install matminer` or `conda install -c conda-forge matminer`

2. **Dataset Download Issues**
   ```
   ERROR: Failed to load dataset matbench_mp_e_form
   ```
   **Solution**: Check internet connection. MatMiner downloads datasets on first use.

3. **Memory Issues**
   ```
   ERROR: MemoryError during dataset loading
   ```
   **Solution**: The full dataset is large (~132k samples). Tests use smaller subsets, but ensure you have sufficient RAM.

4. **Import Path Issues**
   ```
   ERROR: MatBenchDataset import failed: No module named 'ppmat'
   ```
   **Solution**: Run tests from the project root directory, or ensure PYTHONPATH includes the project root.

### Debug Mode

For more detailed output, you can run individual test scripts directly:

```bash
# Run with Python's verbose mode
python -v test_matbench/test_basic.py

# Or add debug prints by modifying the test scripts
```

## Test Coverage

The test suite covers:

- ✅ Basic imports and dependencies
- ✅ MatMiner data loading
- ✅ Dataset initialization with various parameters
- ✅ 5-fold cross-validation protocol
- ✅ Data access and format validation
- ✅ Caching mechanism
- ✅ PaddlePaddle DataLoader integration
- ✅ Error handling and edge cases

## Next Steps

After tests pass, you can:

1. **Compute dataset statistics** for model configuration:
   ```bash
   python test_matbench/compute_matbench_stats.py
   ```

2. **Run training** with the MatBench dataset:
   ```bash
   python property_prediction/train.py -c property_prediction/configs/comformer/comformer_matbench_mp_e_form.yaml
   ```

3. **Extend to other tasks** by modifying the task_name parameter in configs.
