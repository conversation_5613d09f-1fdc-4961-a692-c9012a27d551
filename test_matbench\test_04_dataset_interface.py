#!/usr/bin/env python3
"""
测试模块4: 数据集接口
对应方法: __getitem__, __len__
"""
import sys
from pathlib import Path

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_dataset_interface():
    """测试数据集接口功能"""
    from ppmat.datasets.matbench_dataset import MatBenchDataset
    
    # 创建完整的数据集（不构建图，简化测试）
    dataset = MatBenchDataset(
        path="./data/matbench/matbench_mp_e_form_full.pkl",
        property_names=["e_form"],
        build_structure_cfg={
            "format": "pymatgen_structure",
            "num_cpus": 1
        },
        build_graph_cfg=None,  # 不构建图
        max_samples=5,
        overwrite=True
    )
    
    print(f"数据集接口测试:")
    print(f"  数据集长度: {len(dataset)}")
    
    # 测试__len__方法
    assert len(dataset) == 5
    
    # 测试__getitem__方法
    sample = dataset[0]
    
    print(f"  样本键: {list(sample.keys())}")
    print(f"  graph类型: {type(sample.get('graph'))}")
    print(f"  e_form类型: {type(sample.get('e_form'))}")
    print(f"  e_form形状: {sample.get('e_form').shape}")
    print(f"  id值: {sample.get('id')}")
    
    # 验证样本格式
    required_keys = {'graph', 'e_form', 'id'}
    assert set(sample.keys()) == required_keys
    assert sample['e_form'] is not None
    assert sample['id'] == 0
    
    # 测试多个样本的一致性
    samples = [dataset[i] for i in range(min(3, len(dataset)))]
    first_keys = set(samples[0].keys())
    
    for i, sample in enumerate(samples):
        assert set(sample.keys()) == first_keys, f"样本{i}的键不一致"
        assert sample['id'] == i, f"样本{i}的id不正确"
        print(f"  样本{i}: e_form={sample['e_form'][0]:.4f}")
    
    print("✅ 数据集接口模块测试通过")
    return True

if __name__ == "__main__":
    print("🎯 测试模块4: 数据集接口")
    print("=" * 40)
    
    data_file = project_root / "data" / "matbench" / "matbench_mp_e_form_full.pkl"
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        exit(1)
    
    try:
        test_dataset_interface()
        print("\n🎉 模块4测试成功")
    except Exception as e:
        print(f"\n❌ 模块4测试失败: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
