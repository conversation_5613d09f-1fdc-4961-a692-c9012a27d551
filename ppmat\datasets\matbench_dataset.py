# ppmat/datasets/matbench_dataset.py
import os
import os.path as osp
import pickle
import math
from typing import Optional, Dict, Callable, List
import numpy as np
import pandas as pd
from sklearn.model_selection import KFold, StratifiedKFold
from matminer.datasets import load_dataset
from paddle.io import Dataset

class MatBenchDataset(Dataset):
    """MatBench Dataset Handler for standardized benchmarking"""
    
    # 支持的任务配置
    TASK_CONFIG = {
        "matbench_mp_e_form": {
            "target_col": "e_form",
            "task_type": "regression",
            "unit": "eV/atom",
            "samples": 132752
        }
        # 后续可扩展其他任务
    }
    
    def __init__(self,
                 task_name: str = "matbench_mp_e_form",
                 fold_idx: Optional[int] = None,
                 split: str = "train", 
                 property_names: List[str] = None,
                 build_structure_cfg: Dict = None,
                 build_graph_cfg: Dict = None,
                 transforms: Optional[Callable] = None,
                 cache_path: Optional[str] = None,
                 overwrite: bool = False,
                 filter_unvalid: bool = True,
                 **kwargs):
        
        super().__init__()
        
        # 验证任务名称
        if task_name not in self.TASK_CONFIG:
            raise ValueError(f"Unsupported task: {task_name}")
            
        self.task_name = task_name
        self.task_config = self.TASK_CONFIG[task_name]
        self.fold_idx = fold_idx
        self.split = split
        
        # 设置属性名称
        if property_names is None:
            property_names = [self.task_config["target_col"]]
        self.property_names = property_names
        
        # 其他配置
        self.build_structure_cfg = build_structure_cfg or self._default_structure_cfg()
        self.build_graph_cfg = build_graph_cfg
        self.transforms = transforms
        self.overwrite = overwrite
        self.filter_unvalid = filter_unvalid
        
        # 设置缓存路径
        if cache_path is None:
            cache_path = f"./data/matbench_cache/{task_name}"
            if fold_idx is not None:
                cache_path += f"_fold_{fold_idx}"
        self.cache_path = cache_path
        
        # 加载和处理数据
        self._load_and_process_data()
    
    def _default_structure_cfg(self):
        return {
            "format": "pymatgen_structure",
            "primitive": False,
            "niggli": True,
            "num_cpus": 1,
        }
    
    def _load_and_process_data(self):
        """加载matbench数据并进行预处理"""
        
        # 1. 加载原始数据
        logger.info(f"Loading {self.task_name} dataset...")
        df = load_dataset(self.task_name)
        logger.info(f"Loaded {len(df)} samples")
        
        # 2. 实现5折交叉验证分割
        if self.fold_idx is not None:
            df = self._apply_cv_split(df)
        
        # 3. 转换数据格式
        self.raw_data, self.num_samples = self._convert_data_format(df)
        
        # 4. 处理属性数据
        self.property_data = self._extract_properties(self.raw_data)
        
        # 5. 构建结构和图缓存
        self._build_structure_and_graph_cache()
        
        # 6. 过滤无效数据
        if self.filter_unvalid:
            self._filter_invalid_samples()
    
    def _apply_cv_split(self, df):
        """应用5折交叉验证分割"""
        
        # 使用固定随机种子确保可重现性
        random_state = 18012019
        
        if self.task_config["task_type"] == "regression":
            kf = KFold(n_splits=5, shuffle=True, random_state=random_state)
            y = df[self.task_config["target_col"]].values
        else:
            kf = StratifiedKFold(n_splits=5, shuffle=True, random_state=random_state)
            y = df[self.task_config["target_col"]].values
        
        # 获取当前fold的索引
        folds = list(kf.split(df, y))
        train_idx, test_idx = folds[self.fold_idx]
        
        if self.split == "train":
            return df.iloc[train_idx].reset_index(drop=True)
        else:  # test
            return df.iloc[test_idx].reset_index(drop=True)
    
    def _convert_data_format(self, df):
        """转换数据格式为内部格式"""
        
        data = {
            "structure": [],
            self.task_config["target_col"]: []
        }
        
        for idx, row in df.iterrows():
            data["structure"].append(row["structure"])
            data[self.task_config["target_col"]].append(row[self.task_config["target_col"]])
        
        return data, len(df)
    
    # ... 其他方法实现（类似MP2018Dataset的结构处理逻辑）