# Copyright (c) 2023 PaddlePaddle Authors. All Rights Reserved.

# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at

#     http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from __future__ import absolute_import
from __future__ import annotations

import math
import os
import os.path as osp
import pickle
from collections import defaultdict
from typing import Any
from typing import Callable
from typing import Dict
from typing import Optional
from typing import List

import numpy as np
import paddle.distributed as dist
from paddle.io import Dataset
from sklearn.model_selection import KFold, StratifiedKFold

from ppmat.datasets.build_structure import BuildStructure
from ppmat.datasets.custom_data_type import ConcatData
from ppmat.models import build_graph_converter
from ppmat.utils import logger
from ppmat.utils.io import read_json
from ppmat.utils.misc import is_equal

try:
    from matminer.datasets import load_dataset
    MATMINER_AVAILABLE = True
except ImportError:
    MATMINER_AVAILABLE = False
    logger.warning("matminer not available. MatBench datasets will not work.")

class MatBenchDataset(Dataset):
    """MatBench Dataset Handler for standardized benchmarking

    This class provides utilities for loading and processing MatBench datasets
    for materials science machine learning benchmarks. It supports the standard
    5-fold cross-validation protocol required by MatBench.

    **Supported Tasks**
    Currently supports the following MatBench v0.1 tasks:
    - matbench_mp_e_form: Formation energy prediction (eV/atom)
    - matbench_mp_gap: Band gap prediction (eV)
    - matbench_mp_is_metal: Metallic classification
    - matbench_perovskites: Perovskite formation energy (eV/unit cell)
    - matbench_phonons: Phonon properties (cm^-1)

    **Cross-Validation Protocol**
    Follows MatBench standard:
    - 5-fold cross-validation with fixed random seed (18012019)
    - KFold for regression tasks
    - StratifiedKFold for classification tasks
    - No data leakage between folds

    Args:
        task_name (str): Name of the MatBench task. Defaults to "matbench_mp_e_form".
        fold_idx (Optional[int]): Fold index (0-4) for cross-validation.
            If None, uses full dataset. Defaults to None.
        split (str): Data split to use ("train" or "test"). Defaults to "train".
        property_names (Optional[List[str]]): Property names to extract.
            If None, uses task default. Defaults to None.
        build_structure_cfg (Dict): Configuration for structure building.
        build_graph_cfg (Dict): Configuration for graph building.
        transforms (Optional[Callable]): Data transforms to apply.
        cache_path (Optional[str]): Path for caching processed data.
        overwrite (bool): Whether to overwrite existing cache.
        filter_unvalid (bool): Whether to filter invalid samples.
    """

    # Supported MatBench tasks configuration
    TASK_CONFIG = {
        "matbench_mp_e_form": {
            "target_col": "e_form",
            "task_type": "regression",
            "unit": "eV/atom",
            "samples": 132752,
            "description": "Formation energy prediction from crystal structure"
        },
        "matbench_mp_gap": {
            "target_col": "gap pbe",
            "task_type": "regression",
            "unit": "eV",
            "samples": 106113,
            "description": "Band gap prediction from crystal structure"
        },
        "matbench_mp_is_metal": {
            "target_col": "is_metal",
            "task_type": "classification",
            "unit": "boolean",
            "samples": 106113,
            "description": "Metallic classification from crystal structure"
        },
        "matbench_perovskites": {
            "target_col": "e_form",
            "task_type": "regression",
            "unit": "eV/unit_cell",
            "samples": 18928,
            "description": "Perovskite formation energy prediction"
        },
        "matbench_phonons": {
            "target_col": "last phdos peak",
            "task_type": "regression",
            "unit": "cm^-1",
            "samples": 1265,
            "description": "Phonon properties prediction"
        }
    }
    
    def __init__(
        self,
        task_name: str = "matbench_mp_e_form",
        fold_idx: Optional[int] = None,
        split: str = "train",
        property_names: Optional[List[str]] = None,
        build_structure_cfg: Dict = None,
        build_graph_cfg: Dict = None,
        transforms: Optional[Callable] = None,
        cache_path: Optional[str] = None,
        overwrite: bool = False,
        filter_unvalid: bool = True,
        **kwargs,  # for compatibility
    ):
        super().__init__()

        # Check matminer availability
        if not MATMINER_AVAILABLE:
            raise ImportError(
                "matminer is required for MatBench datasets. "
                "Install with: pip install matminer"
            )

        # Validate task name
        if task_name not in self.TASK_CONFIG:
            available_tasks = list(self.TASK_CONFIG.keys())
            raise ValueError(
                f"Unsupported task: {task_name}. "
                f"Available tasks: {available_tasks}"
            )

        # Validate fold index
        if fold_idx is not None and (fold_idx < 0 or fold_idx > 4):
            raise ValueError("fold_idx must be between 0 and 4 for 5-fold CV")

        # Validate split
        if split not in ["train", "test"]:
            raise ValueError("split must be 'train' or 'test'")

        self.task_name = task_name
        self.task_config = self.TASK_CONFIG[task_name]
        self.fold_idx = fold_idx
        self.split = split

        # Set property names
        if property_names is None:
            property_names = [self.task_config["target_col"]]
        elif isinstance(property_names, str):
            property_names = [property_names]
        self.property_names = property_names

        # Configuration setup
        if build_structure_cfg is None:
            build_structure_cfg = {
                "format": "pymatgen_structure",
                "primitive": False,
                "niggli": True,
                "num_cpus": 1,
            }
            logger.message(
                "The build_structure_cfg is not set, will use the default "
                f"configs: {build_structure_cfg}"
            )

        self.build_structure_cfg = build_structure_cfg
        self.build_graph_cfg = build_graph_cfg
        self.transforms = transforms
        self.overwrite = overwrite
        self.filter_unvalid = filter_unvalid

        # Set cache path
        if cache_path is None:
            cache_path = f"./data/matbench_cache/{task_name}"
            if fold_idx is not None:
                cache_path += f"_fold_{fold_idx}_{split}"
        self.cache_path = cache_path
        logger.info(f"Cache path: {self.cache_path}")

        self.cache_exists = True if osp.exists(self.cache_path) else False

        # Load and process data
        self._load_and_process_data()
    
    def _default_structure_cfg(self):
        return {
            "format": "pymatgen_structure",
            "primitive": False,
            "niggli": True,
            "num_cpus": 1,
        }
    
    def _load_and_process_data(self):
        """Load MatBench data and perform preprocessing"""

        # Load raw data using matminer
        logger.info(f"Loading {self.task_name} dataset...")
        try:
            df = load_dataset(self.task_name)
            logger.info(f"Successfully loaded {len(df)} samples")
        except Exception as e:
            logger.error(f"Failed to load dataset {self.task_name}: {e}")
            raise

        # Apply cross-validation split if specified
        if self.fold_idx is not None:
            df = self._apply_cv_split(df)
            logger.info(f"Applied CV split - Fold {self.fold_idx}, Split {self.split}: {len(df)} samples")

        # Convert to internal format
        self.raw_data, self.num_samples = self._convert_data_format(df)
        logger.info(f"Converted {self.num_samples} samples to internal format")

        # Extract property data
        self.property_data = self._extract_properties(self.raw_data)

        # Handle caching and structure/graph building
        self._handle_caching_and_building()

        # Filter invalid samples if requested
        if self.filter_unvalid:
            self._filter_invalid_samples()
    
    def _apply_cv_split(self, df):
        """应用5折交叉验证分割"""
        
        # 使用固定随机种子确保可重现性
        random_state = 18012019
        
        if self.task_config["task_type"] == "regression":
            kf = KFold(n_splits=5, shuffle=True, random_state=random_state)
            y = df[self.task_config["target_col"]].values
        else:
            kf = StratifiedKFold(n_splits=5, shuffle=True, random_state=random_state)
            y = df[self.task_config["target_col"]].values
        
        # 获取当前fold的索引
        folds = list(kf.split(df, y))
        train_idx, test_idx = folds[self.fold_idx]
        
        if self.split == "train":
            return df.iloc[train_idx].reset_index(drop=True)
        else:  # test
            return df.iloc[test_idx].reset_index(drop=True)
    
    def _convert_data_format(self, df):
        """Convert pandas DataFrame to internal data format"""

        target_col = self.task_config["target_col"]

        # Initialize data dictionary
        data = defaultdict(list)

        # Extract structures and properties
        for _, row in df.iterrows():
            # Store structure (pymatgen Structure object)
            data["structure"].append(row["structure"])

            # Store target property
            data[target_col].append(row[target_col])

            # Store material ID if available
            if "material_id" in row:
                data["material_id"].append(row["material_id"])
            else:
                data["material_id"].append(f"{self.task_name}_{len(data['material_id'])}")

        return dict(data), len(df)

    def _extract_properties(self, data):
        """Extract property data for specified property names"""

        property_data = {}
        target_col = self.task_config["target_col"]

        for property_name in self.property_names:
            if property_name == target_col:
                # Use the target column data
                property_data[property_name] = data[target_col]
            elif property_name in data:
                # Use data if available
                property_data[property_name] = data[property_name]
            else:
                # Map common property name variations
                property_mapping = {
                    "e_form": target_col,
                    "formation_energy_per_atom": target_col,
                    "band_gap": target_col,
                    "gap": target_col,
                    "is_metal": target_col,
                }

                if property_name in property_mapping:
                    mapped_name = property_mapping[property_name]
                    if mapped_name in data:
                        property_data[property_name] = data[mapped_name]
                    else:
                        raise ValueError(f"Property {property_name} not found in data")
                else:
                    raise ValueError(f"Property {property_name} not found in data")

        # Add material IDs if available
        if "material_id" in data:
            property_data["material_id"] = data["material_id"]

        return property_data

    def _handle_caching_and_building(self):
        """Handle caching and structure/graph building similar to MP2018Dataset"""

        # Check cache configuration consistency
        if self.cache_exists and not self.overwrite:
            logger.warning(
                "Cache enabled. If a cache file exists, it will be automatically "
                "read and current settings will be ignored. Please ensure that the "
                "settings used match your current settings."
            )
            try:
                build_structure_cfg_cache = self.load_from_cache(
                    osp.join(self.cache_path, "build_structure_cfg.pkl")
                )
                if is_equal(build_structure_cfg_cache, self.build_structure_cfg):
                    logger.info(
                        "The cached build_structure_cfg configuration matches "
                        "the current settings. Reusing previously generated"
                        " structural data to optimize performance."
                    )
                else:
                    logger.warning(
                        "build_structure_cfg is different from "
                        "build_structure_cfg_cache. Will rebuild the structures and "
                        "graphs."
                    )
                    self.overwrite = True
            except Exception as e:
                logger.warning(e)
                logger.warning(
                    "Failed to load build_structure_cfg.pkl from cache. "
                    "Will rebuild the structures and graphs(if need)."
                )
                self.overwrite = True

            if self.build_graph_cfg is not None and not self.overwrite:
                try:
                    build_graph_cfg_cache = self.load_from_cache(
                        osp.join(self.cache_path, "build_graph_cfg.pkl")
                    )
                    if is_equal(build_graph_cfg_cache, self.build_graph_cfg):
                        logger.info(
                            "The cached build_graph_cfg configuration "
                            "matches the current settings. Reusing previously "
                            "generated graph data to optimize performance."
                        )
                    else:
                        logger.warning(
                            "build_graph_cfg is different from build_graph_cfg_cache"
                            ". Will rebuild the graphs."
                        )
                        self.overwrite = True

                except Exception as e:
                    logger.warning(e)
                    logger.warning(
                        "Failed to load build_graph_cfg.pkl from cache. "
                        "Will rebuild the graphs."
                    )
                    self.overwrite = True

        structure_cache_path = osp.join(self.cache_path, "structures")
        graph_cache_path = osp.join(self.cache_path, "graphs")

        if self.overwrite or not self.cache_exists:
            # Convert structures and graphs
            # Only rank 0 process do the conversion
            if dist.get_rank() == 0:
                # Save build_structure_cfg and build_graph_cfg to cache file
                os.makedirs(self.cache_path, exist_ok=True)
                self.save_to_cache(
                    osp.join(self.cache_path, "build_structure_cfg.pkl"),
                    self.build_structure_cfg,
                )
                self.save_to_cache(
                    osp.join(self.cache_path, "build_graph_cfg.pkl"), self.build_graph_cfg
                )

                # Convert structures - MatBench data already contains pymatgen Structure objects
                structures = self.raw_data["structure"]

                # Save structures to cache file
                os.makedirs(structure_cache_path, exist_ok=True)
                for i in range(self.num_samples):
                    self.save_to_cache(
                        osp.join(structure_cache_path, f"{i:010d}.pkl"),
                        structures[i],
                    )
                logger.info(
                    f"Save {self.num_samples} structures to {structure_cache_path}"
                )

                if self.build_graph_cfg is not None:
                    converter = build_graph_converter(self.build_graph_cfg)
                    graphs = converter(structures)
                    # Save graphs to cache file
                    os.makedirs(graph_cache_path, exist_ok=True)
                    for i in range(self.num_samples):
                        self.save_to_cache(
                            osp.join(graph_cache_path, f"{i:010d}.pkl"), graphs[i]
                        )
                    logger.info(f"Save {self.num_samples} graphs to {graph_cache_path}")

            # Sync all processes
            if dist.is_initialized():
                dist.barrier()

        self.structures = [
            osp.join(structure_cache_path, f"{i:010d}.pkl")
            for i in range(self.num_samples)
        ]
        if self.build_graph_cfg is not None:
            self.graphs = [
                osp.join(graph_cache_path, f"{i:010d}.pkl")
                for i in range(self.num_samples)
            ]
        else:
            self.graphs = None

        assert (
            len(self.structures) == self.num_samples
        ), "The number of structures must be equal to the number of samples."
        assert (
            self.graphs is None or len(self.graphs) == self.num_samples
        ), "The number of graphs must be equal to the number of samples."

    def _filter_invalid_samples(self):
        """Filter out samples with invalid properties"""
        for property_name in self.property_names:
            if property_name not in self.property_data:
                continue

            data = self.property_data[property_name]
            reserve_idx = []
            for i, data_item in enumerate(data):
                if isinstance(data_item, str) or (data_item is not None and not math.isnan(data_item)):
                    reserve_idx.append(i)

            # Update all data structures
            for key in self.property_data.keys():
                self.property_data[key] = [
                    self.property_data[key][i] for i in reserve_idx
                ]

            self.structures = [self.structures[i] for i in reserve_idx]
            if self.graphs is not None:
                self.graphs = [self.graphs[i] for i in reserve_idx]

            logger.warning(
                f"Filter out {len(reserve_idx)} samples with valid properties: "
                f"{property_name}"
            )

        self.num_samples = len(self.structures)
        logger.warning(f"Remaining {self.num_samples} samples after filtering.")

    def save_to_cache(self, cache_path: str, data: Any):
        """Save data to cache file"""
        with open(cache_path, "wb") as f:
            pickle.dump(data, f)

    def load_from_cache(self, cache_path: str):
        """Load data from cache file"""
        if osp.exists(cache_path):
            with open(cache_path, "rb") as f:
                data = pickle.load(f)
            return data
        else:
            raise FileNotFoundError(f"No such file or directory: {cache_path}")

    def get_structure_array(self, structure):
        """Convert pymatgen Structure to array format"""
        atom_types = np.array([site.specie.Z for site in structure])
        # Get lattice parameters and matrix
        lattice_parameters = structure.lattice.parameters
        lengths = np.array(lattice_parameters[:3], dtype="float32").reshape(1, 3)
        angles = np.array(lattice_parameters[3:], dtype="float32").reshape(1, 3)
        lattice = structure.lattice.matrix.astype("float32")

        structure_array = {
            "frac_coords": ConcatData(structure.frac_coords.astype("float32")),
            "cart_coords": ConcatData(structure.cart_coords.astype("float32")),
            "atom_types": ConcatData(atom_types),
            "lattice": ConcatData(lattice.reshape(1, 3, 3)),
            "lengths": ConcatData(lengths),
            "angles": ConcatData(angles),
            "num_atoms": ConcatData(np.array([tuple(atom_types.shape)[0]])),
        }
        return structure_array

    def __getitem__(self, idx: int):
        """Get item at index idx"""
        data = {}
        # Get graph
        if self.graphs is not None:
            graph = self.graphs[idx]
            if isinstance(graph, str):
                graph = self.load_from_cache(graph)
            data["graph"] = graph
        else:
            structure = self.structures[idx]
            if isinstance(structure, str):
                structure = self.load_from_cache(structure)
            data["structure_array"] = self.get_structure_array(structure)

        # Add properties
        for property_name in self.property_names:
            if property_name in self.property_data:
                data[property_name] = np.array(
                    [self.property_data[property_name][idx]]
                ).astype("float32")
            else:
                raise KeyError(f"Property {property_name} not found.")

        # Add ID
        data["id"] = (
            self.property_data["material_id"][idx] if "material_id" in self.property_data else idx
        )

        # Apply transforms
        data = self.transforms(data) if self.transforms is not None else data

        return data

    def __len__(self):
        """Return the number of samples"""
        return self.num_samples