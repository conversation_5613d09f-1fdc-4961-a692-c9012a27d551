#!/usr/bin/env python3
"""
运行所有模块测试
"""
import subprocess
import sys
from pathlib import Path

def run_test(test_file):
    """运行单个测试"""
    print(f"\n{'='*60}")
    print(f"运行测试: {test_file}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            [sys.executable, test_file],
            capture_output=True,
            text=True,
            timeout=300
        )
        
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {test_file} 测试通过")
            return True
        else:
            print(f"❌ {test_file} 测试失败 (返回码: {result.returncode})")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ {test_file} 测试超时")
        return False
    except Exception as e:
        print(f"❌ {test_file} 测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🎯 MatBench模块化测试")
    
    test_files = [
        "test_matbench/test_01_data_loading.py",
        "test_matbench/test_02_structure_processing.py", 
        "test_matbench/test_03_graph_building.py",
        "test_matbench/test_04_dataset_interface.py",
        "test_matbench/test_05_integration.py"
    ]
    
    results = {}
    
    for test_file in test_files:
        results[test_file] = run_test(test_file)
    
    # 总结
    print(f"\n{'='*60}")
    print("测试总结:")
    print(f"{'='*60}")
    
    passed = 0
    failed = 0
    
    for test_file, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_file}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n总计: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("\n🎉 所有测试通过！可以开始训练了")
        print("运行训练命令:")
        print("  python property_prediction/train.py -c test_matbench/test_config_1000.yaml")
    else:
        print(f"\n❌ 有 {failed} 个测试失败，请修复后重试")

if __name__ == "__main__":
    main()
