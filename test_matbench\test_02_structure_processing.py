#!/usr/bin/env python3
"""
测试模块2: 结构处理
对应方法: build_structures, get_structure_array
"""
import sys
import os
from pathlib import Path

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_structure_processing():
    """测试结构处理功能"""
    from ppmat.datasets.matbench_dataset import MatBenchDataset
    from ppmat.datasets.build_structure import BuildStructure
    
    # 创建数据集实例
    dataset = MatBenchDataset.__new__(MatBenchDataset)
    dataset.path = "./data/matbench/matbench_mp_e_form_full.pkl"
    dataset.max_samples = 3
    dataset.cache_path = "./data/matbench_cache/test_structure"
    dataset.overwrite = True
    
    # 读取数据
    data, num_samples = dataset.read_data(dataset.path)
    dataset.row_data = data['structure']
    dataset.num_samples = num_samples
    
    # 设置结构构建器
    build_structure_cfg = {
        "format": "pymatgen_structure",
        "primitive": False,
        "niggli": True,
        "num_cpus": 1,
    }
    dataset.build_structure = BuildStructure(**build_structure_cfg)
    
    # 测试build_structures方法
    structures = dataset.build_structures(overwrite=True)
    
    print(f"结构处理结果:")
    print(f"  结构数量: {len(structures)}")
    print(f"  结构路径示例: {structures[0]}")
    
    # 验证结构文件
    assert len(structures) == num_samples
    assert all(os.path.exists(s) for s in structures)
    
    # 测试加载结构
    import pickle
    with open(structures[0], 'rb') as f:
        structure = pickle.load(f)
    
    print(f"  结构类型: {type(structure)}")
    print(f"  原子数量: {len(structure)}")
    
    # 测试get_structure_array方法
    structure_array = dataset.get_structure_array(structure)
    
    print(f"结构数组结果:")
    print(f"  数组键: {list(structure_array.keys())}")
    print(f"  原子类型形状: {structure_array['atom_types'].data.shape}")
    print(f"  坐标形状: {structure_array['frac_coords'].data.shape}")
    
    # 验证结构数组
    required_keys = {'frac_coords', 'cart_coords', 'atom_types', 'lattice', 'lengths', 'angles', 'num_atoms'}
    assert set(structure_array.keys()) == required_keys
    
    print("✅ 结构处理模块测试通过")
    return True

if __name__ == "__main__":
    print("🎯 测试模块2: 结构处理")
    print("=" * 40)
    
    data_file = project_root / "data" / "matbench" / "matbench_mp_e_form_full.pkl"
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        exit(1)
    
    try:
        test_structure_processing()
        print("\n🎉 模块2测试成功")
    except Exception as e:
        print(f"\n❌ 模块2测试失败: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
