#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for MatBenchDataset functionality.

This script tests different modules of the MatBenchDataset implementation:
1. Basic dataset loading
2. Cross-validation splits
3. Data format conversion
4. Caching mechanism
5. Integration with existing pipeline
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import numpy as np
import pytest

# Import our dataset
from ppmat.datasets.matbench_dataset import MatBenchDataset
from ppmat.utils import logger

# Test configuration
TEST_TASK = "matbench_mp_e_form"
TEST_BATCH_SIZE = 4
TEST_MAX_SAMPLES = 100  # Limit samples for faster testing


def test_matminer_availability():
    """Test 1: Check if matminer is available"""
    print("\n=== Test 1: MatMiner Availability ===")
    
    try:
        from matminer.datasets import load_dataset
        print("✓ matminer is available")
        
        # Try to load a small sample
        df = load_dataset(TEST_TASK)
        print(f"✓ Successfully loaded {TEST_TASK}: {len(df)} samples")
        print(f"✓ Columns: {list(df.columns)}")
        
        return True
    except ImportError as e:
        print(f"✗ matminer not available: {e}")
        return False
    except Exception as e:
        print(f"✗ Error loading dataset: {e}")
        return False


def test_dataset_initialization():
    """Test 2: Basic dataset initialization"""
    print("\n=== Test 2: Dataset Initialization ===")
    
    try:
        # Test basic initialization
        dataset = MatBenchDataset(
            task_name=TEST_TASK,
            fold_idx=None,  # Use full dataset
            split="train",
            property_names=["e_form"],
            cache_path=None,
            overwrite=True,
            filter_unvalid=False
        )
        
        print(f"✓ Dataset initialized successfully")
        print(f"✓ Task: {dataset.task_name}")
        print(f"✓ Samples: {dataset.num_samples}")
        print(f"✓ Properties: {dataset.property_names}")
        print(f"✓ Cache path: {dataset.cache_path}")
        
        return dataset
        
    except Exception as e:
        print(f"✗ Dataset initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_cross_validation_splits():
    """Test 3: Cross-validation splits"""
    print("\n=== Test 3: Cross-Validation Splits ===")
    
    try:
        fold_sizes = []
        
        for fold_idx in range(5):
            # Test train split
            train_dataset = MatBenchDataset(
                task_name=TEST_TASK,
                fold_idx=fold_idx,
                split="train",
                property_names=["e_form"],
                overwrite=True,
                filter_unvalid=False
            )
            
            # Test test split
            test_dataset = MatBenchDataset(
                task_name=TEST_TASK,
                fold_idx=fold_idx,
                split="test",
                property_names=["e_form"],
                overwrite=True,
                filter_unvalid=False
            )
            
            fold_size = {
                'fold': fold_idx,
                'train': train_dataset.num_samples,
                'test': test_dataset.num_samples,
                'total': train_dataset.num_samples + test_dataset.num_samples
            }
            fold_sizes.append(fold_size)
            
            print(f"✓ Fold {fold_idx}: Train={fold_size['train']}, Test={fold_size['test']}")
        
        # Check consistency
        total_samples = fold_sizes[0]['total']
        consistent = all(fold['total'] == total_samples for fold in fold_sizes)
        
        if consistent:
            print(f"✓ All folds have consistent total samples: {total_samples}")
        else:
            print(f"✗ Inconsistent fold sizes: {[fold['total'] for fold in fold_sizes]}")
            
        return fold_sizes
        
    except Exception as e:
        print(f"✗ Cross-validation test failed: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_data_access():
    """Test 4: Data access and format"""
    print("\n=== Test 4: Data Access ===")
    
    try:
        # Create a small dataset for testing
        dataset = MatBenchDataset(
            task_name=TEST_TASK,
            fold_idx=0,
            split="train",
            property_names=["e_form"],
            overwrite=True,
            filter_unvalid=False
        )
        
        print(f"✓ Dataset created with {len(dataset)} samples")
        
        # Test __getitem__
        sample = dataset[0]
        print(f"✓ Sample keys: {list(sample.keys())}")
        
        # Check required keys
        required_keys = ["e_form", "id"]
        for key in required_keys:
            if key in sample:
                print(f"✓ Found required key: {key}")
                print(f"  Type: {type(sample[key])}, Shape: {getattr(sample[key], 'shape', 'N/A')}")
            else:
                print(f"✗ Missing required key: {key}")
        
        # Check structure or graph data
        if "graph" in sample:
            print(f"✓ Found graph data")
        elif "structure_array" in sample:
            print(f"✓ Found structure_array data")
            struct_keys = list(sample["structure_array"].keys())
            print(f"  Structure keys: {struct_keys}")
        else:
            print(f"✗ No graph or structure_array found")
        
        # Test multiple samples
        print(f"\n--- Testing multiple samples ---")
        for i in range(min(3, len(dataset))):
            sample = dataset[i]
            e_form = sample["e_form"]
            print(f"Sample {i}: e_form = {e_form}")
        
        return True
        
    except Exception as e:
        print(f"✗ Data access test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_caching_mechanism():
    """Test 5: Caching mechanism"""
    print("\n=== Test 5: Caching Mechanism ===")
    
    temp_dir = None
    try:
        # Create temporary directory for cache testing
        temp_dir = tempfile.mkdtemp(prefix="matbench_cache_test_")
        print(f"✓ Created temp cache dir: {temp_dir}")
        
        # First load - should create cache
        print("--- First load (creating cache) ---")
        dataset1 = MatBenchDataset(
            task_name=TEST_TASK,
            fold_idx=0,
            split="train",
            property_names=["e_form"],
            cache_path=temp_dir,
            overwrite=True,
            filter_unvalid=False
        )
        
        print(f"✓ First load completed: {len(dataset1)} samples")
        
        # Check if cache files were created
        cache_files = list(Path(temp_dir).rglob("*.pkl"))
        print(f"✓ Cache files created: {len(cache_files)}")
        
        # Second load - should use cache
        print("--- Second load (using cache) ---")
        dataset2 = MatBenchDataset(
            task_name=TEST_TASK,
            fold_idx=0,
            split="train",
            property_names=["e_form"],
            cache_path=temp_dir,
            overwrite=False,  # Use existing cache
            filter_unvalid=False
        )
        
        print(f"✓ Second load completed: {len(dataset2)} samples")
        
        # Verify consistency
        if len(dataset1) == len(dataset2):
            print(f"✓ Cache consistency verified")
        else:
            print(f"✗ Cache inconsistency: {len(dataset1)} vs {len(dataset2)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Caching test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Clean up
        if temp_dir and os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
            print(f"✓ Cleaned up temp directory")


def test_integration_with_dataloader():
    """Test 6: Integration with PaddlePaddle DataLoader"""
    print("\n=== Test 6: DataLoader Integration ===")
    
    try:
        from paddle.io import DataLoader, BatchSampler
        
        # Create dataset
        dataset = MatBenchDataset(
            task_name=TEST_TASK,
            fold_idx=0,
            split="train",
            property_names=["e_form"],
            overwrite=True,
            filter_unvalid=False
        )
        
        print(f"✓ Dataset created: {len(dataset)} samples")
        
        # Create batch sampler
        batch_sampler = BatchSampler(
            dataset=dataset,
            batch_size=TEST_BATCH_SIZE,
            shuffle=False,
            drop_last=False
        )
        
        # Create data loader
        dataloader = DataLoader(
            dataset=dataset,
            batch_sampler=batch_sampler,
            num_workers=0,  # Use 0 for testing to avoid multiprocessing issues
            return_list=True
        )
        
        print(f"✓ DataLoader created")
        
        # Test iteration
        batch_count = 0
        sample_batch = None
        
        for batch in dataloader:
            batch_count += 1
            if batch_count == 1:
                sample_batch = batch
            if batch_count >= 3:  # Test only first few batches
                break
        
        print(f"✓ Processed {batch_count} batches")
        
        if sample_batch:
            print(f"✓ Sample batch keys: {list(sample_batch.keys())}")
            for key, value in sample_batch.items():
                if hasattr(value, 'shape'):
                    print(f"  {key}: shape {value.shape}")
                else:
                    print(f"  {key}: type {type(value)}")
        
        return True
        
    except Exception as e:
        print(f"✗ DataLoader integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_error_handling():
    """Test 7: Error handling"""
    print("\n=== Test 7: Error Handling ===")
    
    test_cases = [
        {
            "name": "Invalid task name",
            "kwargs": {"task_name": "invalid_task"},
            "should_fail": True
        },
        {
            "name": "Invalid fold index",
            "kwargs": {"task_name": TEST_TASK, "fold_idx": 10},
            "should_fail": True
        },
        {
            "name": "Invalid split",
            "kwargs": {"task_name": TEST_TASK, "split": "invalid"},
            "should_fail": True
        },
        {
            "name": "Valid parameters",
            "kwargs": {"task_name": TEST_TASK, "fold_idx": 0, "split": "train"},
            "should_fail": False
        }
    ]
    
    passed_tests = 0
    
    for test_case in test_cases:
        try:
            dataset = MatBenchDataset(
                overwrite=True,
                filter_unvalid=False,
                **test_case["kwargs"]
            )
            
            if test_case["should_fail"]:
                print(f"✗ {test_case['name']}: Should have failed but didn't")
            else:
                print(f"✓ {test_case['name']}: Passed as expected")
                passed_tests += 1
                
        except Exception as e:
            if test_case["should_fail"]:
                print(f"✓ {test_case['name']}: Failed as expected ({type(e).__name__})")
                passed_tests += 1
            else:
                print(f"✗ {test_case['name']}: Unexpected failure: {e}")
    
    print(f"✓ Error handling tests: {passed_tests}/{len(test_cases)} passed")
    return passed_tests == len(test_cases)


def run_all_tests():
    """Run all tests"""
    print("="*80)
    print("MatBenchDataset Module Testing")
    print("="*80)
    
    test_results = {}
    
    # Run tests
    test_results["matminer"] = test_matminer_availability()
    
    if test_results["matminer"]:
        test_results["initialization"] = test_dataset_initialization() is not None
        test_results["cv_splits"] = test_cross_validation_splits() is not None
        test_results["data_access"] = test_data_access()
        test_results["caching"] = test_caching_mechanism()
        test_results["dataloader"] = test_integration_with_dataloader()
        test_results["error_handling"] = test_error_handling()
    else:
        print("\n⚠️  Skipping other tests due to matminer unavailability")
        return False
    
    # Summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    
    passed = sum(test_results.values())
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name:20s}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! MatBenchDataset is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
