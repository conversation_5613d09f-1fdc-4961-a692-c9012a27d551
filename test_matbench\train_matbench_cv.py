# scripts/train_matbench_cv.py
import os
import sys
import yaml
import argparse
from pathlib import Path

def run_cv_training(config_path, task_name="matbench_mp_e_form"):
    """运行5折交叉验证训练"""
    
    results = []
    
    for fold_idx in range(5):
        print(f"\n=== Training Fold {fold_idx} ===")
        
        # 修改配置文件中的fold_idx
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # 更新fold索引
        config['Dataset']['train']['dataset']['__init_params__']['fold_idx'] = fold_idx
        config['Dataset']['val']['dataset']['__init_params__']['fold_idx'] = fold_idx
        
        # 更新输出目录
        config['Trainer']['output_dir'] = f"./output/comformer_{task_name}_fold_{fold_idx}"
        
        # 保存临时配置文件
        temp_config_path = f"temp_config_fold_{fold_idx}.yaml"
        with open(temp_config_path, 'w') as f:
            yaml.dump(config, f)
        
        # 运行训练
        cmd = f"python property_prediction/train.py -c {temp_config_path}"
        os.system(cmd)
        
        # 清理临时文件
        os.remove(temp_config_path)
        
        # 收集结果（这里需要实现结果解析逻辑）
        # results.append(parse_results(fold_idx))
    
    # 计算平均结果
    # print_cv_summary(results)

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("-c", "--config", required=True, help="Config file path")
    parser.add_argument("--task", default="matbench_mp_e_form", help="Task name")
    args = parser.parse_args()
    
    run_cv_training(args.config, args.task)