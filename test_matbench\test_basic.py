#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Basic test for MatBenchDataset - minimal functionality check.
"""

import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test if we can import required modules"""
    print("=== Testing Imports ===")
    
    try:
        # Test matminer import
        from matminer.datasets import load_dataset
        print("✓ matminer imported successfully")
    except ImportError as e:
        print(f"✗ matminer import failed: {e}")
        print("Please install: pip install matminer")
        return False
    
    try:
        # Test our dataset import
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        print("✓ MatBenchDataset imported successfully")
    except ImportError as e:
        print(f"✗ MatBenchDataset import failed: {e}")
        return False
    
    try:
        # Test other required imports
        import numpy as np
        import paddle
        from sklearn.model_selection import KFold
        print("✓ Other dependencies imported successfully")
    except ImportError as e:
        print(f"✗ Dependency import failed: {e}")
        return False
    
    return True


def test_matminer_data_loading():
    """Test basic matminer data loading"""
    print("\n=== Testing MatMiner Data Loading ===")

    try:
        from matminer.datasets import load_dataset

        # Try to load a small sample - use a smaller dataset first
        print("Loading matbench_mp_e_form dataset (this may take a while)...")
        df = load_dataset("matbench_mp_e_form")

        print(f"✓ Dataset loaded successfully")
        print(f"  Samples: {len(df)}")
        print(f"  Columns: {list(df.columns)}")

        # Check required columns
        if "structure" in df.columns and "e_form" in df.columns:
            print("✓ Required columns found")
        else:
            print("✗ Missing required columns")
            return False

        # Check first few samples
        print(f"  First sample e_form: {df['e_form'].iloc[0]}")
        print(f"  Structure type: {type(df['structure'].iloc[0])}")

        # Check data validity
        valid_structures = df['structure'].notna().sum()
        valid_e_forms = df['e_form'].notna().sum()
        print(f"  Valid structures: {valid_structures}/{len(df)}")
        print(f"  Valid e_forms: {valid_e_forms}/{len(df)}")

        return True

    except Exception as e:
        print(f"✗ MatMiner data loading failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_dataset_creation():
    """Test basic dataset creation"""
    print("\n=== Testing Dataset Creation ===")
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        
        print("Creating MatBenchDataset...")
        
        # Create with minimal parameters
        dataset = MatBenchDataset(
            task_name="matbench_mp_e_form",
            fold_idx=0,
            split="train",
            property_names=["e_form"],
            overwrite=True,
            filter_unvalid=False
        )
        
        print(f"✓ Dataset created successfully")
        print(f"  Task: {dataset.task_name}")
        print(f"  Samples: {dataset.num_samples}")
        print(f"  Properties: {dataset.property_names}")
        
        # Test data access
        if len(dataset) > 0:
            sample = dataset[0]
            print(f"✓ Data access works")
            print(f"  Sample keys: {list(sample.keys())}")
            
            if "e_form" in sample:
                print(f"  e_form value: {sample['e_form']}")
            else:
                print("✗ e_form not found in sample")
                return False
        else:
            print("✗ Dataset is empty")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Dataset creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_cross_validation():
    """Test cross-validation functionality"""
    print("\n=== Testing Cross-Validation ===")
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        
        print("Testing 5-fold cross-validation...")
        
        fold_sizes = []
        for fold_idx in range(2):  # Test only first 2 folds for speed
            train_dataset = MatBenchDataset(
                task_name="matbench_mp_e_form",
                fold_idx=fold_idx,
                split="train",
                property_names=["e_form"],
                overwrite=True,
                filter_unvalid=False
            )
            
            test_dataset = MatBenchDataset(
                task_name="matbench_mp_e_form",
                fold_idx=fold_idx,
                split="test",
                property_names=["e_form"],
                overwrite=True,
                filter_unvalid=False
            )
            
            fold_size = {
                'train': len(train_dataset),
                'test': len(test_dataset),
                'total': len(train_dataset) + len(test_dataset)
            }
            fold_sizes.append(fold_size)
            
            print(f"  Fold {fold_idx}: Train={fold_size['train']}, Test={fold_size['test']}")
        
        # Check if folds have reasonable sizes
        if all(fold['train'] > 0 and fold['test'] > 0 for fold in fold_sizes):
            print("✓ Cross-validation splits look reasonable")
            return True
        else:
            print("✗ Cross-validation splits have issues")
            return False
        
    except Exception as e:
        print(f"✗ Cross-validation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run basic tests"""
    print("MatBenchDataset Basic Testing")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("MatMiner Loading", test_matminer_data_loading),
        ("Dataset Creation", test_dataset_creation),
        ("Cross-Validation", test_cross_validation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name:20s}: {status}")
        if result:
            passed += 1
    
    print(f"\nResult: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All basic tests passed!")
        return True
    else:
        print("❌ Some tests failed. Check the output above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
