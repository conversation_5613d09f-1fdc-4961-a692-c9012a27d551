#!/usr/bin/env python3
"""
测试模块1: 数据加载
对应方法: read_data, read_property_data
"""
import sys
from pathlib import Path

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_data_loading():
    """测试数据加载功能"""
    from ppmat.datasets.matbench_dataset import MatBenchDataset
    
    # 创建数据集实例进行测试
    dataset = MatBenchDataset.__new__(MatBenchDataset)
    dataset.path = "./data/matbench/matbench_mp_e_form_full.pkl"
    dataset.max_samples = 5
    
    # 测试read_data方法
    data, num_samples = dataset.read_data(dataset.path)
    
    print(f"数据读取结果:")
    print(f"  样本数: {num_samples}")
    print(f"  数据键: {list(data.keys())}")
    print(f"  结构类型: {type(data['structure'][0])}")
    print(f"  e_form类型: {type(data['e_form'][0])}")
    
    # 验证数据格式
    assert 'structure' in data
    assert 'e_form' in data
    assert len(data['structure']) == num_samples
    assert len(data['e_form']) == num_samples
    
    # 测试read_property_data方法
    dataset.num_samples = num_samples
    property_data = dataset.read_property_data(data, ["e_form"])
    
    print(f"属性数据处理结果:")
    print(f"  属性键: {list(property_data.keys())}")
    print(f"  e_form数量: {len(property_data['e_form'])}")
    
    # 验证属性数据
    assert 'e_form' in property_data
    assert len(property_data['e_form']) == num_samples
    
    print("✅ 数据加载模块测试通过")
    return True

if __name__ == "__main__":
    print("🎯 测试模块1: 数据加载")
    print("=" * 40)
    
    data_file = project_root / "data" / "matbench" / "matbench_mp_e_form_full.pkl"
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        exit(1)
    
    try:
        test_data_loading()
        print("\n🎉 模块1测试成功")
    except Exception as e:
        print(f"\n❌ 模块1测试失败: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
