#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Environment fix script for MatBench testing.
Addresses common compatibility issues.
"""

import subprocess
import sys
import os

def run_command(cmd, description):
    """Run a command and return success status"""
    print(f"\n{description}...")
    print(f"Command: {cmd}")
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Success")
            if result.stdout.strip():
                print(f"Output: {result.stdout.strip()}")
            return True
        else:
            print("✗ Failed")
            if result.stderr.strip():
                print(f"Error: {result.stderr.strip()}")
            return False
            
    except Exception as e:
        print(f"✗ Exception: {e}")
        return False

def fix_matminer_yaml_issue():
    """Fix matminer YAML compatibility issue"""
    print("\n=== Fixing MatMiner YAML Issue ===")
    
    # Try to downgrade PyYAML to compatible version
    commands = [
        ("pip install PyYAML==5.4.1", "Downgrading PyYAML to compatible version"),
        ("pip install matminer==0.8.0", "Installing compatible matminer version"),
    ]
    
    success_count = 0
    for cmd, desc in commands:
        if run_command(cmd, desc):
            success_count += 1
    
    return success_count == len(commands)

def install_matbench_dependencies():
    """Install MatBench dependencies"""
    print("\n=== Installing MatBench Dependencies ===")
    
    commands = [
        ("pip install matbench==0.5", "Installing MatBench"),
        ("pip install scikit-learn", "Installing scikit-learn"),
        ("pip install pandas", "Installing pandas"),
        ("pip install numpy", "Installing numpy"),
    ]
    
    success_count = 0
    for cmd, desc in commands:
        if run_command(cmd, desc):
            success_count += 1
    
    return success_count >= len(commands) - 1  # Allow one failure

def test_imports():
    """Test if imports work after fixes"""
    print("\n=== Testing Imports ===")
    
    test_imports = [
        ("import yaml", "PyYAML"),
        ("from matminer.datasets import load_dataset", "MatMiner"),
        ("import matbench", "MatBench"),
        ("import sklearn", "scikit-learn"),
        ("import pandas", "pandas"),
        ("import numpy", "numpy"),
    ]
    
    success_count = 0
    for import_stmt, name in test_imports:
        try:
            exec(import_stmt)
            print(f"✓ {name} import successful")
            success_count += 1
        except Exception as e:
            print(f"✗ {name} import failed: {e}")
    
    return success_count == len(test_imports)

def main():
    """Main fix routine"""
    print("MatBench Environment Fix Script")
    print("=" * 50)
    
    # Step 1: Fix YAML issue
    yaml_fixed = fix_matminer_yaml_issue()
    
    # Step 2: Install dependencies
    deps_installed = install_matbench_dependencies()
    
    # Step 3: Test imports
    imports_work = test_imports()
    
    # Summary
    print("\n" + "=" * 50)
    print("SUMMARY")
    print("=" * 50)
    
    results = [
        ("YAML Fix", yaml_fixed),
        ("Dependencies", deps_installed),
        ("Import Test", imports_work),
    ]
    
    for name, result in results:
        status = "✓ SUCCESS" if result else "✗ FAILED"
        print(f"{name:15s}: {status}")
    
    if all(result for _, result in results):
        print("\n🎉 Environment fixed successfully!")
        print("\nYou can now run:")
        print("  python test_matbench/test_basic.py")
        return True
    else:
        print("\n❌ Some fixes failed. Manual intervention may be required.")
        print("\nTry these manual steps:")
        print("1. pip uninstall matminer matbench -y")
        print("2. pip install PyYAML==5.4.1")
        print("3. pip install matminer==0.8.0")
        print("4. pip install matbench==0.5")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
